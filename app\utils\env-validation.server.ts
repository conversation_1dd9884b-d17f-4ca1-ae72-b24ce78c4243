/**
 * Environment Variable Validation Utility
 * Ensures all required environment variables are present and valid
 */

interface EnvironmentConfig {
  SHOPIFY_API_KEY: string;
  SHOPIFY_API_SECRET: string;
  SHOPIFY_APP_URL: string;
  SCOPES: string;
  DATABASE_URL: string;
  SESSION_SECRET: string;
  GEMINI_API_KEY?: string;
  NODE_ENV: 'development' | 'production' | 'test';
  BILLING_ENABLED: string;
  BILLING_TRIAL_DAYS: string;
  BILLING_CURRENCY: string;
}

class EnvironmentValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'EnvironmentValidationError';
  }
}

/**
 * Validates all required environment variables
 */
export function validateEnvironment(): EnvironmentConfig {
  const requiredVars = [
    'SHOPIFY_API_KEY',
    'SHOPIFY_API_SECRET', 
    'SHOPIFY_APP_URL',
    'SCOPES',
    'DATABASE_URL',
    'SESSION_SECRET'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    throw new EnvironmentValidationError(
      `Missing required environment variables: ${missingVars.join(', ')}`
    );
  }

  // Validate SHOPIFY_APP_URL format
  const appUrl = process.env.SHOPIFY_APP_URL!;
  if (!appUrl.startsWith('https://')) {
    throw new EnvironmentValidationError(
      'SHOPIFY_APP_URL must be a valid HTTPS URL'
    );
  }

  // Validate URL format
  try {
    new URL(appUrl);
  } catch {
    throw new EnvironmentValidationError(
      'SHOPIFY_APP_URL must be a valid URL'
    );
  }

  // Validate basic scopes are present
  const scopes = process.env.SCOPES!;
  const requiredScopes = [
    'read_products',
    'write_products'
  ];

  const missingScopes = requiredScopes.filter(scope =>
    !scopes.includes(scope)
  );

  if (missingScopes.length > 0) {
    console.warn(
      `⚠️  Missing required scopes: ${missingScopes.join(', ')}`
    );
  }

  // Validate billing configuration
  const billingEnabled = process.env.BILLING_ENABLED || 'true';
  const trialDays = process.env.BILLING_TRIAL_DAYS || '1';
  const currency = process.env.BILLING_CURRENCY || 'USD';

  if (!['true', 'false'].includes(billingEnabled)) {
    throw new EnvironmentValidationError(
      'BILLING_ENABLED must be "true" or "false"'
    );
  }

  if (isNaN(parseInt(trialDays)) || parseInt(trialDays) < 0) {
    throw new EnvironmentValidationError(
      'BILLING_TRIAL_DAYS must be a non-negative number'
    );
  }

  if (!/^[A-Z]{3}$/.test(currency)) {
    throw new EnvironmentValidationError(
      'BILLING_CURRENCY must be a valid 3-letter currency code (e.g., USD)'
    );
  }

  return {
    SHOPIFY_API_KEY: process.env.SHOPIFY_API_KEY!,
    SHOPIFY_API_SECRET: process.env.SHOPIFY_API_SECRET!,
    SHOPIFY_APP_URL: appUrl,
    SCOPES: scopes,
    DATABASE_URL: process.env.DATABASE_URL!,
    SESSION_SECRET: process.env.SESSION_SECRET!,
    GEMINI_API_KEY: process.env.GEMINI_API_KEY,
    NODE_ENV: (process.env.NODE_ENV as any) || 'development',
    BILLING_ENABLED: billingEnabled,
    BILLING_TRIAL_DAYS: trialDays,
    BILLING_CURRENCY: currency
  };
}

/**
 * Gets validated environment configuration
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  try {
    return validateEnvironment();
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    throw error;
  }
}

/**
 * Validates environment on app startup
 */
export function validateEnvironmentOnStartup(): void {
  try {
    const config = validateEnvironment();
    console.log('✅ Environment validation passed');
    console.log(`🌍 App URL: ${config.SHOPIFY_APP_URL}`);
    console.log(`💰 Billing: ${config.BILLING_ENABLED === 'true' ? 'Enabled' : 'Disabled'}`);
    console.log(`🆓 Trial Days: ${config.BILLING_TRIAL_DAYS}`);
    console.log(`💱 Currency: ${config.BILLING_CURRENCY}`);
  } catch (error) {
    console.error('❌ Environment validation failed on startup');
    console.error(error);
    process.exit(1);
  }
}
