import type { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    // Shopify automatically verifies webhook authenticity
    const { payload, session, topic, shop } = await authenticate.webhook(request);
    
    console.log(`Received ${topic} webhook for ${shop}:`, JSON.stringify(payload, null, 2));
    
    if (payload.app_subscription) {
      const subscription = payload.app_subscription;
      
      // TODO: Update subscription in database
      // await db.billingSubscription.upsert({
      //   where: { 
      //     subscriptionId: subscription.id 
      //   },
      //   update: {
      //     status: subscription.status,
      //     trialDays: subscription.trial_days || 0,
      //     currentPeriodEnd: subscription.current_period_end ? new Date(subscription.current_period_end) : null,
      //     updatedAt: new Date()
      //   },
      //   create: {
      //     shop,
      //     subscriptionId: subscription.id,
      //     planId: determinePlanIdFromSubscription(subscription),
      //     status: subscription.status,
      //     trialDays: subscription.trial_days || 0,
      //     currentPeriodEnd: subscription.current_period_end ? new Date(subscription.current_period_end) : null,
      //     priceAmount: subscription.line_items?.[0]?.price ? parseFloat(subscription.line_items[0].price) : null
      //   }
      // });
      
      // TODO: Log billing event
      // await db.billingEvent.create({
      //   data: {
      //     shop,
      //     eventType: 'subscription_updated',
      //     referenceId: subscription.id,
      //     eventData: JSON.stringify(subscription)
      //   }
      // });
      
      // Handle specific status changes
      switch (subscription.status) {
        case 'ACTIVE':
          console.log(`Subscription ${subscription.id} is now active for shop ${shop}`);
          break;
        case 'CANCELLED':
          console.log(`Subscription ${subscription.id} was cancelled for shop ${shop}`);
          break;
        case 'EXPIRED':
          console.log(`Subscription ${subscription.id} has expired for shop ${shop}`);
          break;
        case 'PENDING':
          if (subscription.trial_days && subscription.trial_days > 0) {
            console.log(`Subscription ${subscription.id} is in trial period (${subscription.trial_days} days) for shop ${shop}`);
          } else {
            console.log(`Subscription ${subscription.id} is pending payment for shop ${shop}`);
          }
          break;
      }
    }
    
    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error('App subscription webhook error:', error);
    return new Response("Error processing webhook", { status: 500 });
  }
};

function determinePlanIdFromSubscription(subscription: any): string {
  // This would need to be implemented based on your subscription structure
  // For now, return a default value
  const lineItem = subscription.line_items?.[0];
  if (!lineItem) return 'unknown';
  
  // You might determine this based on price, name, or other attributes
  const price = parseFloat(lineItem.price || '0');
  
  if (price === 199.99) return 'annual';
  if (price === 19.99) return 'monthly';
  
  return 'unknown';
}
