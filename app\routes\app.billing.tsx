import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { BillingService, SubscriptionData } from "../services/billing.server";
import { BillingDashboard } from "../components/billing/BillingDashboard";
import { addCSRFToken } from "../utils/csrf.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    console.log('Billing loader - Request URL:', request.url);
    const { admin, session } = await authenticate.admin(request);
    console.log('Billing loader - Authenticated for shop:', session.shop);

    const billingService = new BillingService(admin, session.shop);

    // Get current subscription status
    const billingStatus = await billingService.hasActiveBilling();
    const subscriptionData = await billingService.getCurrentSubscription();
    const oneTimePurchases = await billingService.getOneTimePurchases();
    const plans = billingService.getAllBillingPlans();
  
  // Calculate monthly usage (mock data for now - would come from database)
  const monthlyUsage = {
    productsOptimized: 45,
    totalSpent: billingStatus.plan?.type === 'pay_per_use' ? 4.50 : (billingStatus.plan?.price || 0),
    lastOptimization: new Date().toISOString()
  };

  // Format recent purchases
  const recentPurchases = oneTimePurchases.data?.currentAppInstallation?.oneTimePurchases?.edges?.map((edge: any) => ({
    id: edge.node.id,
    amount: parseFloat(edge.node.price.amount),
    productCount: Math.round(parseFloat(edge.node.price.amount) / 0.10), // Calculate from amount
    date: edge.node.createdAt,
    status: edge.node.status
  })) || [];

  const data = {
    subscription: billingStatus.subscription,
    plan: billingStatus.plan,
    trialExpired: billingStatus.trialExpired,
    hasAccess: billingStatus.hasAccess,
    monthlyUsage,
    recentPurchases,
    plans,
    shop: session.shop
  };

  // Add CSRF token for security
  return json(addCSRFToken(session.shop, data));
  } catch (error) {
    console.error('Billing loader error:', error);
    // Return default values on error to prevent page crash
    return json({
      subscription: undefined,
      plan: undefined,
      trialExpired: false,
      hasAccess: false,
      monthlyUsage: {
        productsOptimized: 0,
        totalSpent: 0,
        lastOptimization: new Date().toISOString()
      },
      recentPurchases: [],
      plans: [],
      shop: '',
      error: error instanceof Error ? error.message : "Failed to load billing information"
    });
  }
};

export const action = async (args: ActionFunctionArgs) => {
  const { handleBillingAction } = await import("../utils/billing-actions.server");
  return handleBillingAction(args);
};

export default function BillingPage() {
  const {
    subscription,
    plan,
    trialExpired,
    hasAccess,
    monthlyUsage,
    recentPurchases,
    plans,
    csrfToken
  } = useLoaderData<typeof loader>();

  return (
    <>
      <TitleBar title="ProdRankX Billing" />

      {/* Modern Black Hero Section */}
      <div className="min-h-screen bg-black text-white">
        {/* Header Section */}
        <div className="bg-black py-20 px-6">
          <div className="max-w-6xl mx-auto text-center">
            {/* Logo and Title */}
            <div className="flex flex-col items-center mb-12">
              <img
                src="/logo.png"
                alt="ProdRankX Logo"
                className="w-16 h-16 mb-6 rounded-2xl shadow-2xl"
                style={{ filter: 'brightness(1.1) contrast(1.1)' }}
              />
              <h1 style={{
                fontSize: 'clamp(3rem, 8vw, 6rem)',
                fontWeight: 900,
                lineHeight: 0.9,
                letterSpacing: '-0.05em',
                marginBottom: '1rem',
                color: 'white'
              }}>
                BILLING
              </h1>
              <p style={{
                fontSize: 'clamp(1.25rem, 3vw, 1.75rem)',
                fontWeight: 300,
                color: '#cbd5e1',
                maxWidth: '40rem',
                margin: '0 auto'
              }}>
                Manage your ProdRankX subscription and usage
              </p>
            </div>
          </div>
        </div>

        {/* Billing Content */}
        <div className="px-6 pb-20">
          <BillingDashboard
            subscription={subscription as SubscriptionData | undefined}
            plan={plan}
            trialExpired={trialExpired}
            monthlyUsage={monthlyUsage}
            recentPurchases={recentPurchases}
            plans={plans}
            csrfToken={csrfToken}
          />
        </div>
      </div>
    </>
  );
}
