# Shopify Billing Integration Implementation Plan

## Overview
Implement a Shopify Billing API integration for the SEO Bulk Easy app with:
- **1-day free trial** for new users
- **$0.10 per product** usage-based billing model
- Automatic billing through Shopify's merchant invoicing system

## Requirements Analysis

### Business Model
- **Free Trial**: 1 day trial period for new subscribers
- **Usage-Based Pricing**: $0.10 per product optimized
- **Billing Cycle**: 30-day intervals (Shopify standard)
- **Capped Amount**: Set reasonable monthly cap (e.g., $100-500) to prevent unexpected charges

### Technical Requirements
- GraphQL Admin API access for billing operations
- Webhook handling for subscription status changes
- Usage tracking for product optimizations
- Billing state management in the app

## Implementation Steps

### Phase 1: Billing Service Setup

#### 1.1 Create Billing Service
Create `app/services/billing.server.ts`:

```typescript
export class BillingService {
  private admin: any;
  
  constructor(admin: any) {
    this.admin = admin;
  }

  // Create subscription with free trial and usage pricing
  async createSubscription(returnUrl: string) {
    const response = await this.admin.graphql(`
      mutation appSubscriptionCreate($name: String!, $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!, $trialDays: Int) {
        appSubscriptionCreate(name: $name, returnUrl: $returnUrl, lineItems: $lineItems, trialDays: $trialDays) {
          userErrors {
            field
            message
          }
          appSubscription {
            id
            status
            lineItems {
              id
              plan {
                pricingDetails {
                  __typename
                }
              }
            }
          }
          confirmationUrl
        }
      }
    `, {
      variables: {
        name: "SEO Bulk Easy - Product Optimization",
        returnUrl,
        trialDays: 1,
        lineItems: [{
          plan: {
            appUsagePricingDetails: {
              terms: "$0.10 per product optimized",
              cappedAmount: {
                amount: 100.0, // $100 monthly cap
                currencyCode: "USD"
              }
            }
          }
        }]
      }
    });
    
    return response.json();
  }

  // Create usage record for product optimization
  async createUsageRecord(subscriptionLineItemId: string, productCount: number) {
    const amount = productCount * 0.10;
    
    const response = await this.admin.graphql(`
      mutation appUsageRecordCreate($description: String!, $price: MoneyInput!, $subscriptionLineItemId: ID!, $idempotencyKey: String) {
        appUsageRecordCreate(description: $description, price: $price, subscriptionLineItemId: $subscriptionLineItemId, idempotencyKey: $idempotencyKey) {
          userErrors {
            field
            message
          }
          appUsageRecord {
            id
            description
            price {
              amount
              currencyCode
            }
          }
        }
      }
    `, {
      variables: {
        subscriptionLineItemId,
        price: {
          amount,
          currencyCode: "USD"
        },
        description: `SEO optimization for ${productCount} product${productCount > 1 ? 's' : ''}`,
        idempotencyKey: `seo-opt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      }
    });
    
    return response.json();
  }

  // Check current subscription status
  async getCurrentSubscription() {
    const response = await this.admin.graphql(`
      query {
        currentAppInstallation {
          activeSubscriptions {
            id
            name
            status
            trialDays
            currentPeriodEnd
            lineItems {
              id
              plan {
                pricingDetails {
                  ... on AppUsagePricingDetails {
                    cappedAmount {
                      amount
                      currencyCode
                    }
                    terms
                  }
                }
              }
            }
          }
        }
      }
    `);
    
    return response.json();
  }
}
```

#### 1.2 Database Schema Updates
Add billing-related fields to track subscription state:

```sql
-- Add to existing schema or create new table
ALTER TABLE sessions ADD COLUMN subscription_id VARCHAR(255);
ALTER TABLE sessions ADD COLUMN subscription_status VARCHAR(50);
ALTER TABLE sessions ADD COLUMN trial_ends_at TIMESTAMP;
ALTER TABLE sessions ADD COLUMN usage_line_item_id VARCHAR(255);

-- Or create dedicated billing table
CREATE TABLE app_billing (
  id SERIAL PRIMARY KEY,
  shop VARCHAR(255) NOT NULL,
  subscription_id VARCHAR(255),
  subscription_status VARCHAR(50),
  trial_ends_at TIMESTAMP,
  usage_line_item_id VARCHAR(255),
  monthly_usage_count INTEGER DEFAULT 0,
  monthly_usage_amount DECIMAL(10,2) DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Phase 2: Billing Integration Points

#### 2.1 App Installation Flow
Modify `app/routes/app._index.tsx` to check billing status and prompt for subscription:

```typescript
// In loader function
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  
  const billingService = new BillingService(admin);
  const subscriptionData = await billingService.getCurrentSubscription();
  
  // Check if user needs to subscribe
  const needsSubscription = !subscriptionData.data.currentAppInstallation.activeSubscriptions.length;
  
  return json({
    // ... existing data
    needsSubscription,
    subscriptionData: subscriptionData.data
  });
};
```

#### 2.2 Subscription Creation Route
Create `app/routes/app.billing.tsx`:

```typescript
export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "create_subscription") {
    const billingService = new BillingService(admin);
    const returnUrl = `${process.env.SHOPIFY_APP_URL}/app/billing/callback`;
    
    const result = await billingService.createSubscription(returnUrl);
    
    if (result.data.appSubscriptionCreate.userErrors.length === 0) {
      // Redirect to confirmation URL
      return redirect(result.data.appSubscriptionCreate.confirmationUrl);
    } else {
      return json({ error: result.data.appSubscriptionCreate.userErrors });
    }
  }
  
  return json({ error: "Invalid action" });
};
```

#### 2.3 Billing Callback Handler
Create `app/routes/app.billing.callback.tsx`:

```typescript
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const url = new URL(request.url);
  const charge_id = url.searchParams.get("charge_id");
  
  if (charge_id) {
    // Store subscription info in database
    const billingService = new BillingService(admin);
    const subscriptionData = await billingService.getCurrentSubscription();
    
    // Update database with subscription details
    // ... database update logic
    
    return redirect("/app?billing=success");
  }
  
  return redirect("/app?billing=cancelled");
};
```

### Phase 3: Usage Tracking Integration

#### 3.1 Modify SEO Optimization Flow
Update `app/routes/app.seo-dashboard.tsx` to create usage records:

```typescript
// In the optimization action
if (action === 'optimize_comprehensive') {
  // ... existing optimization logic
  
  // After successful optimization, create usage record
  const billingService = new BillingService(admin);
  const subscriptionData = await billingService.getCurrentSubscription();
  
  if (subscriptionData.data.currentAppInstallation.activeSubscriptions.length > 0) {
    const subscription = subscriptionData.data.currentAppInstallation.activeSubscriptions[0];
    const usageLineItem = subscription.lineItems.find(item => 
      item.plan.pricingDetails.__typename === 'AppUsagePricingDetails'
    );
    
    if (usageLineItem) {
      await billingService.createUsageRecord(
        usageLineItem.id, 
        selectedProducts.length
      );
    }
  }
  
  // ... rest of optimization logic
}
```

### Phase 4: Webhook Handling

#### 4.1 Subscription Status Webhooks
Create `app/routes/webhooks.app.subscriptions.update.tsx`:

```typescript
export const action = async ({ request }: ActionFunctionArgs) => {
  const { payload, session, topic, shop } = await authenticate.webhook(request);
  
  console.log(`Received ${topic} webhook for ${shop}`);
  
  // Update subscription status in database
  if (payload.app_subscription) {
    // Update billing record
    await db.appBilling.update({
      where: { shop },
      data: {
        subscription_status: payload.app_subscription.status,
        updated_at: new Date()
      }
    });
  }
  
  return new Response();
};
```

#### 4.2 Approaching Capped Amount Webhook
Create `app/routes/webhooks.app.subscriptions.approaching_capped_amount.tsx`:

```typescript
export const action = async ({ request }: ActionFunctionArgs) => {
  const { payload, session, topic, shop } = await authenticate.webhook(request);
  
  console.log(`Received ${topic} webhook for ${shop} - approaching usage cap`);
  
  // Notify user they're approaching their monthly limit
  // Could send email, show in-app notification, etc.
  
  return new Response();
};
```

### Phase 5: UI Components

#### 5.1 Billing Status Component
Create `app/components/billing/BillingStatus.tsx`:

```typescript
interface BillingStatusProps {
  subscription: any;
  usageCount: number;
  usageAmount: number;
}

export function BillingStatus({ subscription, usageCount, usageAmount }: BillingStatusProps) {
  const cappedAmount = subscription?.lineItems[0]?.plan?.pricingDetails?.cappedAmount?.amount || 0;
  const usagePercentage = (usageAmount / cappedAmount) * 100;
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Billing Status</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between text-sm">
              <span>Products Optimized This Month</span>
              <span>{usageCount}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Current Usage</span>
              <span>${usageAmount.toFixed(2)} / ${cappedAmount}</span>
            </div>
            <Progress value={usagePercentage} className="mt-2" />
          </div>
          
          {subscription?.status === 'ACTIVE' && (
            <Badge variant="success">Active Subscription</Badge>
          )}
          
          {subscription?.trialDays > 0 && (
            <Badge variant="info">Free Trial Active</Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
```

#### 5.2 Subscription Prompt Component
Create `app/components/billing/SubscriptionPrompt.tsx`:

```typescript
export function SubscriptionPrompt() {
  const fetcher = useFetcher();
  
  return (
    <Card className="border-2 border-primary">
      <CardHeader>
        <CardTitle>Start Your Free Trial</CardTitle>
        <CardDescription>
          Get 1 day free, then pay only $0.10 per product optimized
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            <ul className="space-y-1">
              <li>✓ 1-day free trial</li>
              <li>✓ $0.10 per product optimized</li>
              <li>✓ $100 monthly cap for protection</li>
              <li>✓ Cancel anytime</li>
            </ul>
          </div>
          
          <fetcher.Form method="post" action="/app/billing">
            <input type="hidden" name="action" value="create_subscription" />
            <Button type="submit" className="w-full" disabled={fetcher.state === "submitting"}>
              {fetcher.state === "submitting" ? "Setting up..." : "Start Free Trial"}
            </Button>
          </fetcher.Form>
        </div>
      </CardContent>
    </Card>
  );
}
```

## Testing Strategy

### 5.1 Development Testing
1. Use Shopify's test mode for billing API calls
2. Test with development stores
3. Verify webhook delivery in development

### 5.2 Production Considerations
1. Set up proper error handling for billing failures
2. Implement retry logic for failed usage records
3. Monitor billing webhook delivery
4. Set up alerts for billing issues

## Security Considerations

1. **Idempotency**: Use unique idempotency keys for usage records
2. **Validation**: Validate all billing-related inputs
3. **Rate Limiting**: Implement rate limiting for billing operations
4. **Audit Trail**: Log all billing operations for debugging

## Deployment Checklist

- [ ] Billing service implemented and tested
- [ ] Database schema updated
- [ ] Webhook endpoints created and registered
- [ ] UI components integrated
- [ ] Error handling implemented
- [ ] Testing completed in development environment
- [ ] Monitoring and alerting set up
- [ ] Documentation updated

## Estimated Timeline

- **Phase 1**: Billing Service Setup - 2-3 days
- **Phase 2**: Integration Points - 2-3 days  
- **Phase 3**: Usage Tracking - 1-2 days
- **Phase 4**: Webhook Handling - 1-2 days
- **Phase 5**: UI Components - 2-3 days
- **Testing & Refinement**: 2-3 days

**Total Estimated Time**: 10-16 days

## Next Steps

1. Review and approve this implementation plan
2. Set up development environment for billing testing
3. Begin Phase 1 implementation
4. Test each phase thoroughly before proceeding
5. Deploy to production with careful monitoring
