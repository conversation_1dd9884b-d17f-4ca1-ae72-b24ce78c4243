import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";

// Handle GET requests (when users return from Shopify billing)
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const charge_id = url.searchParams.get('charge_id');

  if (charge_id) {
    // User returned from Shopify billing, redirect to SEO dashboard to continue optimization
    console.log('🔄 User returned from billing, redirecting to SEO dashboard');
    return redirect('/app/seo-dashboard?billing_complete=true');
  }

  // If no charge_id, redirect to dashboard
  return redirect('/app/seo-dashboard');
};

export const action = async (args: ActionFunctionArgs) => {
  const { handleBillingAction } = await import("../utils/billing-actions.server");
  return handleBillingAction(args);
};
