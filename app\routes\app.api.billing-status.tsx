import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { BillingService } from "../services/billing.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const { admin, session } = await authenticate.admin(request);
    
    const billingService = new BillingService(admin, session.shop);
    const billingStatus = await billingService.hasActiveBilling();
    
    return json({
      hasAccess: billingStatus.hasAccess,
      plan: billingStatus.plan,
      subscription: billingStatus.subscription,
      trialExpired: billingStatus.trialExpired,
      isLoading: false
    });
  } catch (error) {
    console.error('Billing status API error:', error);
    return json({
      hasAccess: false,
      isLoading: false,
      error: error instanceof Error ? error.message : "Failed to check billing status"
    }, { status: 500 });
  }
};
