import * as React from "react";
import { useFetcher } from "@remix-run/react";
import { motion } from "framer-motion";
import { Button as UIButton } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  DollarSign,
  Zap,
  CreditCard,
  AlertCircle,
  CheckCircle,
  Calculator,
  X,
  Shield,
  Lock
} from "lucide-react";

interface PayPerUseConfirmationProps {
  productCount: number;
  selectedProducts: string[];
  onConfirm: () => void;
  onCancel: () => void;
  isProcessing?: boolean;
  csrfToken: string;
}

export function PayPerUseConfirmation({
  productCount,
  selectedProducts,
  onConfirm,
  onCancel,
  isProcessing = false,
  csrfToken
}: PayPerUseConfirmationProps) {
  const totalCost = productCount * 0.10;
  const fetcher = useFetcher();

  // Handle fetcher state changes and errors
  React.useEffect(() => {
    console.log('🔄 PayPerUse fetcher state:', fetcher.state);
    console.log('📋 PayPerUse fetcher data:', fetcher.data);

    if (fetcher.data && typeof fetcher.data === 'object') {
      const data = fetcher.data as any;

      if (data.error) {
        console.error('❌ Pay-per-use billing error:', data.error);
        alert(`Payment Error: ${data.error}`);
      } else if (data.success && data.confirmationUrl) {
        console.log('✅ Pay-per-use purchase created successfully, redirecting...');

        try {
          // Use window.top to break out of iframe context
          if (window.top) {
            window.top.location.href = data.confirmationUrl;
          } else {
            window.location.href = data.confirmationUrl;
          }
        } catch (error) {
          console.error('❌ Failed to redirect to confirmation URL:', error);
          alert('Purchase created successfully, but failed to redirect. Please refresh the page.');
        }
      }
    }
  }, [fetcher.data, fetcher.state]);

  const handlePayAndOptimize = () => {
    // Validate inputs
    if (!productCount || productCount <= 0) {
      alert('Invalid product count');
      return;
    }

    if (!selectedProducts || selectedProducts.length === 0) {
      alert('No products selected');
      return;
    }

    if (selectedProducts.length !== productCount) {
      alert('Selected products count does not match product count');
      return;
    }

    // Check if already processing
    if (fetcher.state === 'submitting' || fetcher.state === 'loading') {
      console.log('⏳ Already processing purchase request...');
      return;
    }

    console.log('🔄 Creating pay-per-use purchase:', {
      productCount,
      selectedProductsCount: selectedProducts.length,
      totalCost
    });

    // Convert product IDs to GID format if they're not already
    const formattedProductIds = selectedProducts.map(id =>
      id.startsWith('gid://shopify/Product/') ? id : `gid://shopify/Product/${id}`
    );

    console.log('🔄 Converting product IDs to GID format:', {
      original: selectedProducts.slice(0, 3),
      formatted: formattedProductIds.slice(0, 3)
    });

    const formData = new FormData();
    formData.append('action', 'create_pay_per_use_purchase');
    formData.append('productCount', productCount.toString());
    formData.append('selectedProducts', JSON.stringify(formattedProductIds));
    formData.append('csrfToken', csrfToken);

    console.log('🔐 PayPerUseConfirmation - Submitting with CSRF token');
    console.log('🔐 CSRF token length:', csrfToken?.length || 0);
    console.log('📋 Pay-per-use form data:', {
      action: 'create_pay_per_use_purchase',
      productCount: productCount.toString(),
      selectedProducts: JSON.stringify(selectedProducts),
      csrfTokenPresent: !!csrfToken
    });

    fetcher.submit(formData, {
      method: 'POST',
      action: '/app/billing/pay-per-use'
    });
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 10 }}
        transition={{ duration: 0.2, ease: "easeOut" }}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-sm border border-gray-100"
      >
        {/* Header */}
        <div className="px-6 py-5 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-black rounded-full flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Pay & Optimize</h3>
                <p className="text-sm text-gray-500">{productCount} product{productCount > 1 ? 's' : ''}</p>
              </div>
            </div>
            <button
              onClick={onCancel}
              className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
            >
              <X className="w-4 h-4 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-5 space-y-5">
          {/* Cost Summary */}
          <div className="bg-gray-50 rounded-xl p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-gray-600">Total Cost</span>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">${totalCost.toFixed(2)}</div>
                <div className="text-xs text-gray-500">${productCount} × $0.10</div>
              </div>
            </div>
            <div className="flex items-center text-xs text-gray-500">
              <CheckCircle className="w-3 h-3 mr-1" />
              Instant optimization • No subscription required
            </div>
          </div>

          {/* Security Notice */}
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <Shield className="w-4 h-4 text-gray-600" />
            <div className="text-xs text-gray-600">
              <span className="font-medium">Secure payment</span> via Shopify billing
            </div>
          </div>

          {/* What's Included */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-900">What's included:</h4>
            <div className="space-y-1.5 text-xs text-gray-600">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-3 h-3 text-gray-400" />
                <span>AI-powered SEO optimization</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-3 h-3 text-gray-400" />
                <span>Meta descriptions & titles</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-3 h-3 text-gray-400" />
                <span>Instant results</span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-100 space-y-3">
          <div className="flex space-x-3">
            <UIButton
              onClick={onCancel}
              disabled={isProcessing || fetcher.state === 'submitting'}
              className="flex-1 h-10 bg-gray-100 hover:bg-gray-200 text-gray-700 border-0"
            >
              Cancel
            </UIButton>
            <UIButton
              onClick={handlePayAndOptimize}
              disabled={isProcessing || fetcher.state === 'submitting'}
              className="flex-1 h-10 bg-black hover:bg-gray-800 text-white border-0"
            >
              {fetcher.state === 'submitting' ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  <span>Processing...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4" />
                  <span>Pay ${totalCost.toFixed(2)}</span>
                </div>
              )}
            </UIButton>
          </div>
          <p className="text-xs text-gray-500 text-center">
            Secure payment via Shopify • Charges are non-refundable
          </p>
        </div>
      </motion.div>
    </div>
  );
}

// Success confirmation component
export function PayPerUseSuccess({ 
  productCount, 
  totalCost, 
  onContinue 
}: { 
  productCount: number; 
  totalCost: number; 
  onContinue: () => void; 
}) {
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="w-full max-w-md"
      >
        <Card>
          <CardHeader className="text-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <CardTitle className="text-xl text-green-700">Payment Successful!</CardTitle>
            <CardDescription>
              Your payment has been processed and optimization is starting
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="bg-green-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-700 mb-1">
                ${totalCost.toFixed(2)}
              </div>
              <div className="text-sm text-green-600">
                Charged for {productCount} product{productCount > 1 ? 's' : ''}
              </div>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-600 mb-4">
                Optimization is now in progress. You can monitor the status in your dashboard.
              </p>
              
              <UIButton onClick={onContinue} className="w-full">
                Continue to Dashboard
              </UIButton>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
