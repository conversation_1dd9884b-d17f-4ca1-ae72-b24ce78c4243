// Webhook registration utility
export async function registerBillingWebhooks(admin: any) {
  const webhooks = [
    {
      topic: 'APP_SUBSCRIPTIONS_UPDATE',
      address: `${process.env.SHOPIFY_APP_URL}/webhooks/app_subscriptions/update`,
      format: 'JSON'
    },
    {
      topic: 'APP_PURCHASES_ONE_TIME_UPDATE', 
      address: `${process.env.SHOPIFY_APP_URL}/webhooks/app_purchases_one_time/update`,
      format: 'JSON'
    }
  ];

  for (const webhook of webhooks) {
    try {
      const response = await admin.graphql(`
        mutation webhookSubscriptionCreate($topic: WebhookSubscriptionTopic!, $webhookSubscription: WebhookSubscriptionInput!) {
          webhookSubscriptionCreate(topic: $topic, webhookSubscription: $webhookSubscription) {
            webhookSubscription {
              id
              callbackUrl
              topic
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        variables: {
          topic: webhook.topic,
          webhookSubscription: {
            callbackUrl: webhook.address,
            format: webhook.format
          }
        }
      });
      
      console.log(`✅ Registered webhook: ${webhook.topic}`);
    } catch (error) {
      console.error(`❌ Failed to register webhook ${webhook.topic}:`, error);
    }
  }
}

// Call this during app installation
export async function setupAppBilling(admin: any) {
  await registerBillingWebhooks(admin);
  console.log('🎯 Billing webhooks registered successfully');
}
