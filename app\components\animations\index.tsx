// Simple, lightweight animation components for performance

export function LoadingAnimation({ size = 24 }: { size?: number }) {
  return (
    <div 
      className="animate-spin rounded-full border-2 border-current border-t-transparent"
      style={{ width: size, height: size }}
    />
  );
}

export function SuccessAnimation({ size = 24 }: { size?: number }) {
  return (
    <div 
      className="flex items-center justify-center text-green-500"
      style={{ width: size, height: size }}
    >
      ✓
    </div>
  );
}

export function ProcessingAnimation({ size = 24 }: { size?: number }) {
  return (
    <div 
      className="animate-pulse flex items-center justify-center"
      style={{ width: size, height: size }}
    >
      <div className="w-2 h-2 bg-current rounded-full mr-1 animate-bounce" />
      <div className="w-2 h-2 bg-current rounded-full mr-1 animate-bounce" style={{ animationDelay: '0.1s' }} />
      <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
    </div>
  );
}

export function FloatingElementsAnimation({ size = 100 }: { size?: number }) {
  return (
    <div 
      className="relative opacity-20"
      style={{ width: size, height: size }}
    >
      <div className="absolute top-0 left-0 w-4 h-4 bg-white rounded-full animate-pulse" />
      <div className="absolute top-4 right-0 w-3 h-3 bg-white rounded-full animate-pulse" style={{ animationDelay: '0.5s' }} />
      <div className="absolute bottom-0 left-4 w-2 h-2 bg-white rounded-full animate-pulse" style={{ animationDelay: '1s' }} />
    </div>
  );
}

export function MorphingShapeAnimation({ size = 24 }: { size?: number }) {
  return (
    <div 
      className="animate-pulse bg-current rounded"
      style={{ width: size, height: size }}
    />
  );
}
