import { Button } from "@/components/ui/button";

interface SeoOptimizationHeaderProps {
  totalProducts: number;
  selectedCount: number;
  averageSeoScore: number;
  onOptimizeSelected: () => void;
  onSelectAll: () => void;
  isOptimizing: boolean;
}

export function SeoOptimizationHeader({
  totalProducts,
  selectedCount,
  averageSeoScore,
  onOptimizeSelected,
  onSelectAll,
  isOptimizing
}: SeoOptimizationHeaderProps) {
  return (
    <div className="relative bg-black text-white py-20 px-6 overflow-hidden">
      {/* Simple static background */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-white rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white rounded-full blur-2xl" />
      </div>

      <div className="max-w-6xl mx-auto relative z-10">
        <div className="text-center">
          {/* Logo and Badge */}
          <div className="flex flex-col items-center mb-12">
            <img
              src="/logo.png"
              alt="ProdRankX Logo"
              className="w-16 h-16 mb-6 rounded-2xl shadow-2xl"
              style={{ filter: 'brightness(1.1) contrast(1.1)' }}
            />
            <div className="inline-flex items-center bg-white/10 rounded-full px-8 py-4 border border-white/20">
              <div className="w-3 h-3 bg-white rounded-full mr-4" />
              <span className="text-sm font-semibold tracking-widest uppercase">SEO OPTIMIZER</span>
            </div>
          </div>

          <h1
            style={{
              fontSize: 'clamp(4rem, 12vw, 12rem)',
              fontWeight: 900,
              lineHeight: 0.85,
              letterSpacing: '-0.08em',
              marginBottom: '2rem',
              color: 'white'
            }}
          >
            PRODRANK
            <br />
            <span style={{ color: '#64748b' }}>X</span>
          </h1>

          <p
            style={{
              fontSize: 'clamp(1.5rem, 4vw, 2.5rem)',
              fontWeight: 300,
              color: '#cbd5e1',
              lineHeight: 1.4,
              maxWidth: '60rem',
              margin: '0 auto 1.5rem auto'
            }}
          >
            AI-powered bulk SEO optimization for your entire product catalog
          </p>

          <p
            style={{
              fontSize: 'clamp(1.125rem, 2.5vw, 1.5rem)',
              fontWeight: 400,
              color: '#94a3b8',
              maxWidth: '40rem',
              margin: '0 auto 3rem auto',
              letterSpacing: '0.05em'
            }}
          >
            Select products • Configure settings • Optimize instantly
          </p>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center mt-8">
            <Button
              onClick={onSelectAll}
              size="lg"
              className="bg-white/10 text-white border-2 border-white/40 hover:bg-white hover:text-black font-bold py-4 px-8 rounded-2xl"
            >
              Select All ({totalProducts})
            </Button>

            <Button
              onClick={onOptimizeSelected}
              disabled={selectedCount === 0 || isOptimizing}
              size="lg"
              className="bg-white text-black hover:bg-gray-100 disabled:opacity-50 font-bold py-4 px-8 rounded-2xl"
            >
              {isOptimizing ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
                  <span>Optimizing...</span>
                </div>
              ) : (
                `Optimize ${selectedCount} Selected`
              )}
            </Button>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12 max-w-4xl mx-auto">
            <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-6 border border-white/20 text-center">
              <div className="text-4xl font-bold mb-2">{selectedCount}</div>
              <div className="text-white/70">Selected Products</div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-6 border border-white/20 text-center">
              <div className="text-4xl font-bold mb-2">{averageSeoScore}</div>
              <div className="text-white/70">Average SEO Score</div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-6 border border-white/20 text-center">
              <div className="text-4xl font-bold mb-2">{totalProducts}</div>
              <div className="text-white/70">Total Products</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
