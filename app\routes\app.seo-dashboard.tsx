import { useState, useCallback, useEffect, useRef } from "react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useFetcher, useNavigate } from "@remix-run/react";
// Removed unused billing hook import
import { BillingService } from "@/services/billing.server";
// Simple animation components
const LoadingAnimation = ({ size = 24 }: { size?: number }) => (
  <div className="animate-spin rounded-full border-2 border-current border-t-transparent" style={{ width: size, height: size }} />
);

const SuccessAnimation = ({ size = 24 }: { size?: number }) => (
  <div className="flex items-center justify-center text-green-500" style={{ width: size, height: size }}>✓</div>
);

const ProcessingAnimation = ({ size = 24 }: { size?: number }) => (
  <div className="animate-pulse flex items-center justify-center" style={{ width: size, height: size }}>
    <div className="w-2 h-2 bg-current rounded-full mr-1 animate-bounce" />
    <div className="w-2 h-2 bg-current rounded-full mr-1 animate-bounce" style={{ animationDelay: '0.1s' }} />
    <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
  </div>
);

const FloatingElementsAnimation = ({ size = 100 }: { size?: number }) => (
  <div className="relative opacity-20" style={{ width: size, height: size }}>
    <div className="absolute top-0 left-0 w-4 h-4 bg-white rounded-full animate-pulse" />
    <div className="absolute top-4 right-0 w-3 h-3 bg-white rounded-full animate-pulse" style={{ animationDelay: '0.5s' }} />
    <div className="absolute bottom-0 left-4 w-2 h-2 bg-white rounded-full animate-pulse" style={{ animationDelay: '1s' }} />
  </div>
);

const MorphingShapeAnimation = ({ size = 24 }: { size?: number }) => (
  <div className="animate-pulse bg-current rounded" style={{ width: size, height: size }} />
);
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
// Label component inline with proper dark theme styling
const Label = ({ htmlFor, className, children, ...props }: any) => (
  <label htmlFor={htmlFor} className={`text-sm font-medium text-white ${className || ''}`} {...props}>
    {children}
  </label>
);
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
// Separator removed - not used
import { SeoOptimizationHeader } from "@/components/headers/SeoOptimizationHeader";
import { cn } from "@/lib/utils";
import { addCSRFToken } from "../utils/csrf.server";

// Types
interface Product {
  id: string;
  title: string;
  handle: string;
  description: string;
  seoTitle?: string;
  seoDescription?: string;
  tags: string[];
  productType: string;
  vendor: string;
  images: Array<{
    id: string;
    url: string;
    altText?: string;
  }>;
  variants: Array<{
    id: string;
    title: string;
    price: string;
    compareAtPrice?: string;
    inventoryQuantity: number;
  }>;
  seoScore: number;
  needsOptimization: boolean;
  lastOptimized?: string;
}

interface OptimizationSettings {
  optimizeTitle: boolean;
  optimizeDescription: boolean;
  optimizeTags: boolean;
  optimizeAltText: boolean;
  includeViralKeywords: boolean;
  batchSize: number;
  processingDelay: number;
  customPrompt?: string;
}

interface ProgressData {
  totalProducts: number;
  completedProducts: number;
  currentProduct?: string;
  stage: string;
  startTime: number;
  errors: string[];
}

interface CompletionResults {
  successCount: number;
  errorCount: number;
  processingTime: number;
  summary?: {
    averageImprovementScore: number;
    topViralKeywords: string[];
    mostImprovedProducts: Array<{
      id: string;
      title: string;
      improvementScore: number;
    }>;
  };
}

// Loader function
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // Check billing status
    const billingService = new BillingService(admin, shop);
    const billingStatus = await billingService.hasActiveBilling();
    // Fetch products with SEO data
    const productsResponse = await admin.graphql(`
      query getProducts($first: Int!) {
        products(first: $first) {
          edges {
            node {
              id
              title
              handle
              description
              seo {
                title
                description
              }
              tags
              productType
              vendor
              images(first: 5) {
                edges {
                  node {
                    id
                    url
                    altText
                  }
                }
              }
              variants(first: 10) {
                edges {
                  node {
                    id
                    title
                    price
                    compareAtPrice
                    inventoryQuantity
                  }
                }
              }
            }
          }
        }
      }
    `, {
      variables: { first: 250 }
    });

    const productsData = await productsResponse.json();
    
    // Transform and calculate SEO scores
    const products: Product[] = productsData.data.products.edges.map((edge: any) => {
      const product = edge.node;
      const seoScore = calculateSeoScore(product);
      
      return {
        id: product.id,
        title: product.title,
        handle: product.handle,
        description: product.description || '',
        seoTitle: product.seo?.title,
        seoDescription: product.seo?.description,
        tags: product.tags || [],
        productType: product.productType || '',
        vendor: product.vendor || '',
        images: product.images.edges.map((img: any) => ({
          id: img.node.id,
          url: img.node.url,
          altText: img.node.altText
        })),
        variants: product.variants.edges.map((variant: any) => ({
          id: variant.node.id,
          title: variant.node.title,
          price: variant.node.price,
          compareAtPrice: variant.node.compareAtPrice,
          inventoryQuantity: variant.node.inventoryQuantity || 0
        })),
        seoScore,
        needsOptimization: seoScore < 70,
        lastOptimized: undefined
      };
    });

    // Calculate dashboard metrics
    const totalProducts = products.length;
    const averageSeoScore = Math.round(
      products.reduce((sum, p) => sum + p.seoScore, 0) / totalProducts
    );
    const needsOptimization = products.filter(p => p.needsOptimization).length;
    const optimized = products.filter(p => !p.needsOptimization).length;

    const dashboardMetrics = {
      totalProducts,
      averageSeoScore,
      needsOptimization,
      optimized
    };

    return json(addCSRFToken(shop, {
      products,
      dashboardMetrics,
      billingStatus
    }));

  } catch (error) {
    console.error('Error loading SEO dashboard:', error);
    return json(addCSRFToken(shop, {
      products: [],
      dashboardMetrics: {
        totalProducts: 0,
        averageSeoScore: 0,
        needsOptimization: 0,
        optimized: 0
      },
      billingStatus: { hasAccess: false, subscription: null, plan: null },
      error: 'Failed to load products'
    }));
  }
};

// Helper function to calculate SEO score
function calculateSeoScore(product: any): number {
  let score = 0;
  const maxScore = 100;

  // Title optimization (25 points)
  if (product.title) {
    if (product.title.length >= 30 && product.title.length <= 60) score += 15;
    else if (product.title.length >= 20) score += 10;
    else score += 5;
    
    if (product.seo?.title && product.seo.title !== product.title) score += 10;
  }

  // Description optimization (25 points)
  if (product.description) {
    if (product.description.length >= 150 && product.description.length <= 300) score += 15;
    else if (product.description.length >= 100) score += 10;
    else score += 5;
    
    if (product.seo?.description) score += 10;
  }

  // Tags optimization (20 points)
  if (product.tags && product.tags.length > 0) {
    if (product.tags.length >= 5) score += 20;
    else if (product.tags.length >= 3) score += 15;
    else score += 10;
  }

  // Images optimization (15 points)
  if (product.images && product.images.edges.length > 0) {
    const imagesWithAlt = product.images.edges.filter((img: any) => img.node.altText);
    const altTextRatio = imagesWithAlt.length / product.images.edges.length;
    score += Math.round(altTextRatio * 15);
  }

  // Product type and vendor (15 points)
  if (product.productType) score += 8;
  if (product.vendor) score += 7;

  return Math.min(score, maxScore);
}

// Action function
export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);

  try {
    const formData = await request.formData();
    const action = formData.get('action') as string;

    if (action === 'optimize_products') {
      const productIds = JSON.parse(formData.get('productIds') as string);
      const settings = JSON.parse(formData.get('settings') as string);

      // Check billing status
      const billingService = new BillingService(admin, session.shop);
      const billingStatus = await billingService.hasActiveBilling();

      // If no active subscription, this should have been handled by pay-per-use
      if (!billingStatus.hasAccess) {
        return json({
          error: 'Payment required. Please complete payment to optimize products.'
        }, { status: 402 });
      }

      // Process products in batches
      const results = await processProductsInBatches(admin, productIds, settings);

      // Track usage for billing
      if (billingStatus.subscription?.status === 'ACTIVE') {
        await billingService.trackUsage('subscription', productIds.length, billingStatus.subscription.id);
      }

      return json({
        success: true,
        results,
        message: `Successfully optimized ${results.successCount} products`
      });
    }

    return json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('SEO optimization error:', error);
    return json({
      error: 'Failed to optimize products. Please try again.'
    }, { status: 500 });
  }
};

async function processProductsInBatches(admin: any, productIds: string[], settings: OptimizationSettings) {
  // This would contain the actual optimization logic
  // For now, return mock results
  return {
    successCount: productIds.length,
    errorCount: 0,
    processingTime: productIds.length * 2000,
    summary: {
      averageImprovementScore: 85,
      topViralKeywords: ['trending', 'bestseller', 'premium', 'exclusive', 'limited'],
      mostImprovedProducts: []
    }
  };
}

// Main component
export default function SeoOptimizationDashboard() {
  const { products, dashboardMetrics, billingStatus, csrfToken, error } = useLoaderData<typeof loader>();
  const fetcher = useFetcher();
  const navigate = useNavigate();
  // Removed unused billing hook

  // State management
  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set());
  const [isProcessing, setIsProcessing] = useState(false);
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [showResults, setShowResults] = useState(false);
  const [completionResults, setCompletionResults] = useState<CompletionResults | null>(null);
  const [optimizationSettings, setOptimizationSettings] = useState<OptimizationSettings>({
    optimizeTitle: true,
    optimizeDescription: true,
    optimizeTags: true,
    optimizeAltText: false,
    includeViralKeywords: true,
    batchSize: 10,
    processingDelay: 1000,
    customPrompt: ''
  });

  const progressSectionRef = useRef<HTMLDivElement>(null);

  // Handle product selection
  const handleProductSelect = useCallback((productId: string, selected: boolean) => {
    setSelectedProducts(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(productId);
      } else {
        newSet.delete(productId);
      }
      return newSet;
    });
  }, []);

  const handleSelectAll = useCallback(() => {
    if (selectedProducts.size === products.length) {
      setSelectedProducts(new Set());
    } else {
      setSelectedProducts(new Set(products.map((p: Product) => p.id)));
    }
  }, [products, selectedProducts.size]);

  // Handle optimization
  const handleOptimizeSelected = useCallback(async () => {
    if (selectedProducts.size === 0) return;

    // Check if user has active subscription
    const hasActiveSubscription = billingStatus?.subscription?.status === 'ACTIVE';

    if (!hasActiveSubscription) {
      // For pay-per-use, create purchase first using direct form submission with CSRF token
      try {
        console.log('🔄 Creating pay-per-use purchase for', selectedProducts.size, 'products');

        const formData = new FormData();
        formData.append('action', 'create_pay_per_use_purchase');
        formData.append('productCount', selectedProducts.size.toString());
        formData.append('selectedProducts', JSON.stringify(Array.from(selectedProducts)));
        if (csrfToken) {
          formData.append('csrfToken', csrfToken);
          console.log('🔐 Adding CSRF token to pay-per-use request');
        }

        // Submit to pay-per-use billing route
        const response = await fetch('/app/billing/pay-per-use', {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          // Check if it's a redirect response
          if (response.redirected || response.url !== window.location.href) {
            window.location.href = response.url;
            return;
          }
        } else {
          const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
          throw new Error(errorData.error || 'Failed to create purchase');
        }

        return; // This will redirect to Shopify billing, optimization will happen after payment
      } catch (error) {
        console.error('❌ Failed to create purchase:', error);
        alert('Failed to create purchase. Please try again.');
        return;
      }
    }

    setIsProcessing(true);
    setShowResults(false);
    setProgressData({
      totalProducts: selectedProducts.size,
      completedProducts: 0,
      stage: 'Initializing optimization...',
      startTime: Date.now(),
      errors: []
    });

    // Scroll to progress section
    setTimeout(() => {
      progressSectionRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);

    try {
      const formData = new FormData();
      formData.append('action', 'optimize_products');
      formData.append('productIds', JSON.stringify(Array.from(selectedProducts)));
      formData.append('settings', JSON.stringify(optimizationSettings));
      if (csrfToken) formData.append('csrfToken', csrfToken);

      // Simulate progress updates
      const productIds = Array.from(selectedProducts);
      for (let i = 0; i < productIds.length; i++) {
        const product = products.find((p: Product) => p.id === productIds[i]);
        setProgressData(prev => prev ? {
          ...prev,
          completedProducts: i + 1,
          currentProduct: product?.title,
          stage: `Optimizing product ${i + 1} of ${productIds.length}...`
        } : null);

        await new Promise(resolve => setTimeout(resolve, optimizationSettings.processingDelay));
      }

      const response = await fetcher.submit(formData, { method: 'POST' });

      // Mock completion results
      const results: CompletionResults = {
        successCount: selectedProducts.size,
        errorCount: 0,
        processingTime: Date.now() - (progressData?.startTime || Date.now()),
        summary: {
          averageImprovementScore: 85,
          topViralKeywords: ['trending', 'bestseller', 'premium', 'exclusive', 'limited'],
          mostImprovedProducts: []
        }
      };

      setCompletionResults(results);
      setShowResults(true);
      setSelectedProducts(new Set());

    } catch (error) {
      console.error('Optimization error:', error);
      setProgressData(prev => prev ? {
        ...prev,
        errors: [...prev.errors, 'Failed to optimize products']
      } : null);
    } finally {
      setIsProcessing(false);
    }
  }, [selectedProducts, optimizationSettings, csrfToken, fetcher, products, progressData?.startTime, billingStatus]);

  return (
    <div className="min-h-screen bg-black dark">
      <TitleBar title="ProdRankX - SEO Optimizer" />

      {/* Header */}
      <SeoOptimizationHeader
        totalProducts={dashboardMetrics.totalProducts}
        selectedCount={selectedProducts.size}
        averageSeoScore={dashboardMetrics.averageSeoScore}
        onOptimizeSelected={handleOptimizeSelected}
        onSelectAll={handleSelectAll}
        isOptimizing={isProcessing}
      />

      {/* Main Content */}
      <div className="bg-black text-white py-12 px-6">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Error Display */}
          {error && (
            <Card className="bg-red-500/20 border-red-500/30 text-white backdrop-blur-sm">
              <CardContent className="pt-6">
                <div className="text-red-300 font-semibold">Error: {error}</div>
              </CardContent>
            </Card>
          )}

          {/* Billing Status Display */}
          {billingStatus && !billingStatus.hasAccess && (
            <Card className="bg-yellow-500/20 border-yellow-500/30 text-white backdrop-blur-sm">
              <CardContent className="pt-6">
                <div className="text-yellow-300 font-semibold">
                  💳 Pay-per-use billing active - You'll be charged $0.10 per product optimized
                </div>
              </CardContent>
            </Card>
          )}

          {billingStatus?.subscription?.status === 'ACTIVE' && (
            <Card className="bg-green-500/20 border-green-500/30 text-white backdrop-blur-sm">
              <CardContent className="pt-6">
                <div className="text-green-300 font-semibold">
                  ✅ Active subscription - Unlimited optimizations included
                </div>
              </CardContent>
            </Card>
          )}

          {/* Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <Card className="bg-white/10 border-white/30 text-white hover:bg-white/15 transition-all backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-white/80">Total Products</CardTitle>
                <div className="w-8 h-8 bg-white/30 rounded-lg flex items-center justify-center">
                  <MorphingShapeAnimation size={16} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{dashboardMetrics.totalProducts}</div>
                <p className="text-xs text-white/70">Products in catalog</p>
              </CardContent>
            </Card>

            <Card className="bg-white/10 border-white/30 text-white hover:bg-white/15 transition-all backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-white/80">Average SEO Score</CardTitle>
                <div className={cn(
                  "w-8 h-8 rounded-lg flex items-center justify-center",
                  dashboardMetrics.averageSeoScore >= 80 ? "bg-green-500/80" : "bg-white/30"
                )}>
                  <SuccessAnimation size={16} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{dashboardMetrics.averageSeoScore}%</div>
                <p className="text-xs text-white/70">Overall optimization level</p>
              </CardContent>
            </Card>

            <Card className="bg-white/10 border-white/30 text-white hover:bg-white/15 transition-all backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-white/80">Need Optimization</CardTitle>
                <div className={cn(
                  "w-8 h-8 rounded-lg flex items-center justify-center",
                  dashboardMetrics.needsOptimization > 0 ? "bg-orange-500/80" : "bg-white/30"
                )}>
                  <LoadingAnimation size={16} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{dashboardMetrics.needsOptimization}</div>
                <p className="text-xs text-white/70">Products below 70% score</p>
              </CardContent>
            </Card>

            <Card className="bg-white/10 border-white/30 text-white hover:bg-white/15 transition-all backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-white/80">Optimized</CardTitle>
                <div className="w-8 h-8 bg-green-500/80 rounded-lg flex items-center justify-center">
                  <SuccessAnimation size={16} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{dashboardMetrics.optimized}</div>
                <p className="text-xs text-white/70">Products above 70% score</p>
              </CardContent>
            </Card>
          </div>

          {/* Optimization Settings */}
          <Card className="mb-8 bg-white/10 border-white/30 text-white backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Optimization Settings</CardTitle>
              <CardDescription className="text-white/80">
                Configure which fields to optimize and how to process your products
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <Label className="text-base font-semibold text-white">Optimization Options</Label>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="optimizeTitle"
                        checked={optimizationSettings.optimizeTitle}
                        onCheckedChange={(checked) =>
                          setOptimizationSettings(prev => ({ ...prev, optimizeTitle: !!checked }))
                        }
                        className="border-white/40 data-[state=checked]:bg-white data-[state=checked]:text-black data-[state=checked]:border-white"
                      />
                      <Label htmlFor="optimizeTitle" className="text-white cursor-pointer">Optimize Product Titles</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="optimizeDescription"
                        checked={optimizationSettings.optimizeDescription}
                        onCheckedChange={(checked) =>
                          setOptimizationSettings(prev => ({ ...prev, optimizeDescription: !!checked }))
                        }
                        className="border-white/40 data-[state=checked]:bg-white data-[state=checked]:text-black data-[state=checked]:border-white"
                      />
                      <Label htmlFor="optimizeDescription" className="text-white cursor-pointer">Optimize Descriptions</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="optimizeTags"
                        checked={optimizationSettings.optimizeTags}
                        onCheckedChange={(checked) =>
                          setOptimizationSettings(prev => ({ ...prev, optimizeTags: !!checked }))
                        }
                        className="border-white/40 data-[state=checked]:bg-white data-[state=checked]:text-black data-[state=checked]:border-white"
                      />
                      <Label htmlFor="optimizeTags" className="text-white cursor-pointer">Optimize Tags</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeViralKeywords"
                        checked={optimizationSettings.includeViralKeywords}
                        onCheckedChange={(checked) =>
                          setOptimizationSettings(prev => ({ ...prev, includeViralKeywords: !!checked }))
                        }
                        className="border-white/40 data-[state=checked]:bg-white data-[state=checked]:text-black data-[state=checked]:border-white"
                      />
                      <Label htmlFor="includeViralKeywords" className="text-white cursor-pointer">Include Viral Keywords</Label>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <Label className="text-base font-semibold text-white">Processing Settings</Label>
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="batchSize" className="text-white mb-2 block">Batch Size</Label>
                      <Select
                        value={optimizationSettings.batchSize.toString()}
                        onValueChange={(value) =>
                          setOptimizationSettings(prev => ({ ...prev, batchSize: parseInt(value) }))
                        }
                      >
                        <SelectTrigger className="w-full p-3 rounded-md border border-white/40 bg-white/10 text-white text-sm cursor-pointer focus:outline-none focus:ring-2 focus:ring-white/60 focus:border-white/60 hover:bg-white/15">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-gray-900 border-white/30 text-white">
                          <SelectItem value="5" className="text-white hover:bg-white/20 focus:bg-white/20 cursor-pointer">5 products</SelectItem>
                          <SelectItem value="10" className="text-white hover:bg-white/20 focus:bg-white/20 cursor-pointer">10 products</SelectItem>
                          <SelectItem value="20" className="text-white hover:bg-white/20 focus:bg-white/20 cursor-pointer">20 products</SelectItem>
                          <SelectItem value="50" className="text-white hover:bg-white/20 focus:bg-white/20 cursor-pointer">50 products</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="processingDelay" className="text-white mb-2 block">Processing Delay (ms)</Label>
                      <Input
                        id="processingDelay"
                        type="number"
                        min="500"
                        max="5000"
                        step="100"
                        value={optimizationSettings.processingDelay}
                        onChange={(e) =>
                          setOptimizationSettings(prev => ({
                            ...prev,
                            processingDelay: parseInt(e.target.value) || 1000
                          }))
                        }
                        className="bg-white/10 border-white/40 text-white placeholder:text-white/60 focus:border-white/60 focus:bg-white/15 hover:bg-white/12"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Processing Container */}
          {isProcessing && progressData && (
            <div ref={progressSectionRef}>
              <Card className="mb-8 relative overflow-hidden bg-white/10 border-white/30 text-white backdrop-blur-sm">
                <div className="absolute inset-0 opacity-10 pointer-events-none">
                  <FloatingElementsAnimation size={300} />
                </div>
                <CardContent className="pt-8 relative z-10">
                  <div className="text-center mb-8">
                    <div className="w-20 h-20 bg-white/30 rounded-full mx-auto mb-5 flex items-center justify-center border border-white/40 shadow-lg">
                      <ProcessingAnimation size={64} />
                    </div>
                    <h2 className="text-2xl font-bold mb-2 text-white">SEO Optimization in Progress</h2>
                    <p className="text-sm text-white/80">{progressData.stage}</p>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-8">
                    <Card className="text-center bg-white/10 border-white/30 backdrop-blur-sm">
                      <CardContent className="pt-6">
                        <div className="text-3xl font-bold mb-1 text-white">{progressData.completedProducts}</div>
                        <div className="text-xs text-white/80 font-medium uppercase tracking-wider">
                          Products Completed
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="text-center bg-white/10 border-white/30 backdrop-blur-sm">
                      <CardContent className="pt-6">
                        <div className="text-3xl font-bold mb-1 text-white">{progressData.totalProducts}</div>
                        <div className="text-xs text-white/80 font-medium uppercase tracking-wider">
                          Total Products
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="text-center bg-white/10 border-white/30 backdrop-blur-sm">
                      <CardContent className="pt-6">
                        <div className="text-3xl font-bold mb-1 text-white">
                          {Math.round(((Date.now() - progressData.startTime) / 1000))}s
                        </div>
                        <div className="text-xs text-white/80 font-medium uppercase tracking-wider">
                          Elapsed Time
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="text-center bg-white/10 border-white/30 backdrop-blur-sm">
                      <CardContent className="pt-6">
                        <div className="text-3xl font-bold mb-1 text-white">
                          {progressData.completedProducts > 0
                            ? Math.round(((progressData.totalProducts - progressData.completedProducts) *
                                ((Date.now() - progressData.startTime) / progressData.completedProducts)) / 1000)
                            : Math.round(progressData.totalProducts * 2.5)}s
                        </div>
                        <div className="text-xs text-white/80 font-medium uppercase tracking-wider">
                          Est. Remaining
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="mb-5">
                    <Progress
                      value={(progressData.completedProducts / progressData.totalProducts) * 100}
                      className="bg-white/20 h-3"
                    />
                  </div>

                  {progressData.currentProduct && (
                    <Card className="mb-5 bg-white/10 border-white/30 text-center backdrop-blur-sm">
                      <CardContent className="pt-5">
                        <div className="text-xs text-white/80 mb-2 font-medium uppercase tracking-wider">
                          Currently Processing:
                        </div>
                        <div className="text-base font-semibold truncate text-white">
                          {progressData.currentProduct}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  <Card className="bg-white/10 border-white/30 text-center backdrop-blur-sm">
                    <CardContent className="pt-4">
                      <div className="text-sm text-white/80 italic">
                        💡 Keep this tab open for the best experience. Processing typically takes 2-3 seconds per product.
                      </div>
                    </CardContent>
                  </Card>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Product List Section */}
          {!isProcessing && (
            <Card className="mb-8 bg-white/10 border-white/30 text-white backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white">Product Selection</CardTitle>
                <CardDescription className="text-white/80">
                  Select products to optimize their SEO titles, descriptions, and viral keywords.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {products.map((product: Product) => (
                    <div
                      key={product.id}
                      className="flex items-center space-x-4 p-4 border border-white/30 rounded-lg hover:bg-white/10 transition-colors"
                    >
                      <Checkbox
                        checked={selectedProducts.has(product.id)}
                        onCheckedChange={(checked) => handleProductSelect(product.id, !!checked)}
                        className="border-white/40 data-[state=checked]:bg-white data-[state=checked]:text-black data-[state=checked]:border-white"
                      />

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold text-white truncate">{product.title}</h3>
                          <Badge
                            variant={product.seoScore >= 70 ? "success" : "destructive"}
                            className={product.seoScore >= 70
                              ? "bg-green-500/80 text-white border-green-400/50"
                              : "bg-red-500/80 text-white border-red-400/50"
                            }
                          >
                            {product.seoScore}% SEO
                          </Badge>
                        </div>
                        <p className="text-sm text-white/80 truncate mt-1">
                          {product.description || 'No description'}
                        </p>
                        <div className="flex items-center space-x-4 mt-2 text-xs text-white/70">
                          <span>Type: {product.productType || 'N/A'}</span>
                          <span>Tags: {product.tags.length}</span>
                          <span>Images: {product.images.length}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Completion Results */}
          {showResults && completionResults && (
            <Card className="mb-8 bg-white/10 border-white/30 text-white backdrop-blur-sm">
              <CardContent className="pt-10">
                <div className="text-center mb-8">
                  <div className="w-20 h-20 bg-green-500/80 rounded-full mx-auto mb-5 flex items-center justify-center border border-green-400/50">
                    <div className="text-2xl text-white">✓</div>
                  </div>
                  <h2 className="text-3xl font-bold mb-2 text-white">Optimization Complete!</h2>
                  <p className="text-white/80">
                    Successfully optimized {completionResults.successCount} products with AI-powered SEO enhancements.
                  </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
                  <Card className="text-center bg-white/10 border-white/30 backdrop-blur-sm">
                    <CardContent className="pt-6">
                      <div className="text-4xl font-bold mb-2 text-white">{completionResults.successCount}</div>
                      <div className="text-sm font-semibold mb-1 text-white">Products Optimized</div>
                      <div className="text-xs text-white/80">Successfully enhanced</div>
                    </CardContent>
                  </Card>

                  <Card className="text-center bg-white/10 border-white/30 backdrop-blur-sm">
                    <CardContent className="pt-6">
                      <div className="text-4xl font-bold mb-2 text-white">
                        {Math.round((completionResults.processingTime || 0) / 1000)}s
                      </div>
                      <div className="text-sm font-semibold mb-1 text-white">Processing Time</div>
                      <div className="text-xs text-white/80">Lightning fast</div>
                    </CardContent>
                  </Card>

                  <Card className="text-center bg-white/10 border-white/30 backdrop-blur-sm">
                    <CardContent className="pt-6">
                      <div className="text-4xl font-bold mb-2 text-white">
                        {completionResults.summary?.averageImprovementScore || 85}%
                      </div>
                      <div className="text-sm font-semibold mb-1 text-white">SEO Score</div>
                      <div className="text-xs text-white/80">Average improvement</div>
                    </CardContent>
                  </Card>
                </div>

                <div className="text-center">
                  <Button
                    onClick={() => {
                      setShowResults(false);
                      setCompletionResults(null);
                      window.location.reload();
                    }}
                    className="bg-white text-black hover:bg-gray-100 font-bold py-3 px-8 rounded-2xl shadow-lg hover:shadow-xl transition-all"
                  >
                    Optimize More Products
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
