import type { LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { BillingService } from "../services/billing.server";
import db from "../db.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const url = new URL(request.url);
  
  // Get charge_id from URL parameters (for subscriptions)
  const charge_id = url.searchParams.get("charge_id");
  
  // Get purchase_id from URL parameters (for one-time purchases)
  const purchase_id = url.searchParams.get("purchase_id");
  
  const billingService = new BillingService(admin, session.shop);
  
  try {
    if (charge_id) {
      // Handle subscription confirmation
      console.log(`Subscription confirmed for shop ${session.shop}, charge_id: ${charge_id}`);
      
      // Get updated subscription data
      const subscriptionData = await billingService.getCurrentSubscription();
      const activeSubscriptions = subscriptionData.data?.currentAppInstallation?.activeSubscriptions || [];
      
      if (activeSubscriptions.length > 0) {
        const subscription = activeSubscriptions[0];

        try {
          // Store subscription details in database
          await db.billingSubscription.upsert({
            where: { subscriptionId: subscription.id },
            update: {
              status: subscription.status,
              planId: determinePlanId(subscription),
              trialDays: subscription.trialDays || 0,
              currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
              updatedAt: new Date()
            },
            create: {
              shop: session.shop,
              subscriptionId: subscription.id,
              status: subscription.status,
              planId: determinePlanId(subscription),
              trialDays: subscription.trialDays || 0,
              trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1000) : null,
              currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
              priceAmount: getSubscriptionPrice(subscription),
              priceCurrency: 'USD'
            }
          });

          // Log billing event
          await db.billingEvent.create({
            data: {
              shop: session.shop,
              eventType: 'subscription_confirmed',
              referenceId: subscription.id,
              eventData: JSON.stringify(subscription)
            }
          });

          console.log(`✅ Subscription confirmed and stored for shop ${session.shop}`);
        } catch (dbError) {
          console.error('Failed to store subscription confirmation in database:', dbError);
          // Don't fail the whole operation if database fails
        }

        return redirect("/app?billing=success&type=subscription");
      }
    }
    
    if (purchase_id) {
      // Handle one-time purchase confirmation
      console.log(`One-time purchase confirmed for shop ${session.shop}, purchase_id: ${purchase_id}`);
      
      // Get purchase details
      const purchaseData = await billingService.getOneTimePurchases();
      const purchases = purchaseData.data?.currentAppInstallation?.oneTimePurchases?.edges || [];
      const purchase = purchases.find((edge: any) => edge.node.id === purchase_id)?.node;
      
      if (purchase) {
        try {
          // Store purchase details in database
          await db.billingPurchase.upsert({
            where: { purchaseId: purchase.id },
            update: {
              status: purchase.status,
              amount: parseFloat(purchase.price.amount),
              productCount: Math.round((parseFloat(purchase.price.amount) - 0.50) / 0.10),
              description: purchase.name,
              updatedAt: new Date()
            },
            create: {
              shop: session.shop,
              purchaseId: purchase.id,
              status: purchase.status,
              amount: parseFloat(purchase.price.amount),
              productCount: Math.round((parseFloat(purchase.price.amount) - 0.50) / 0.10),
              currency: purchase.price.currencyCode || 'USD',
              description: purchase.name
            }
          });

          // Log billing event
          await db.billingEvent.create({
            data: {
              shop: session.shop,
              eventType: 'purchase_confirmed',
              referenceId: purchase.id,
              eventData: JSON.stringify(purchase)
            }
          });

          console.log(`✅ Purchase confirmed and stored for shop ${session.shop}`);
        } catch (dbError) {
          console.error('Failed to store purchase confirmation in database:', dbError);
          // Don't fail the whole operation if database fails
        }

        // For SEO optimization purchases, redirect to SEO dashboard to continue optimization
        if (purchase.name && purchase.name.includes('SEO Optimization')) {
          return redirect("/app/seo-dashboard?billing_complete=true");
        }

        return redirect("/app?billing=success&type=purchase");
      }
    }
    
    // If we get here, something went wrong
    console.error(`Billing callback failed for shop ${session.shop}. charge_id: ${charge_id}, purchase_id: ${purchase_id}`);
    return redirect("/app?billing=error");
    
  } catch (error) {
    console.error('Billing callback error:', error);
    return redirect("/app?billing=error");
  }
};

// Helper function to determine plan ID from subscription data
function determinePlanId(subscription: any): string {
  const lineItem = subscription.lineItems?.[0];
  if (!lineItem) return 'unknown';
  
  const pricingDetails = lineItem.plan?.pricingDetails;
  if (!pricingDetails) return 'unknown';
  
  if (pricingDetails.interval === 'ANNUAL') {
    return 'annual';
  } else if (pricingDetails.interval === 'EVERY_30_DAYS') {
    return 'monthly';
  }
  
  return 'unknown';
}

// Helper function to get subscription price from subscription data
function getSubscriptionPrice(subscription: any): number {
  const lineItem = subscription.lineItems?.[0];
  if (!lineItem) return 0;

  const pricingDetails = lineItem.plan?.pricingDetails;
  if (!pricingDetails) return 0;

  // Return price based on plan type
  if (pricingDetails.interval === 'ANNUAL') {
    return 199.99; // Annual plan price
  } else if (pricingDetails.interval === 'EVERY_30_DAYS') {
    return 19.99; // Monthly plan price
  }

  return 0;
}
