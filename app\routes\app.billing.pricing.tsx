import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { BillingService } from "../services/billing.server";
import { PricingSelection } from "../components/billing/PricingSelection";
import { PageContent } from "@/components/ui/page-header";
import { addCSRFToken } from "../utils/csrf.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  
  const billingService = new BillingService(admin, session.shop);
  
  // Get current billing status
  const billingStatus = await billingService.hasActiveBilling();
  const plans = billingService.getAllBillingPlans();
  
  const data = {
    plans,
    currentPlan: billingStatus.plan,
    hasAccess: billingStatus.hasAccess,
    shop: session.shop
  };

  return json(addCSRFToken(session.shop, data));
};

export const action = async (args: ActionFunctionArgs) => {
  const { handleBillingAction } = await import("../utils/billing-actions.server");
  return handleBillingAction(args);
};

export default function BillingPricingPage() {
  const { plans, currentPlan, csrfToken } = useLoaderData<typeof loader>();

  return (
    <>
      <TitleBar title="Choose Your Plan" />
      <PageContent>
        <PricingSelection
          plans={plans}
          selectedPlan={currentPlan?.id}
          showPayPerUse={true}
          csrfToken={csrfToken}
        />
      </PageContent>
    </>
  );
}
