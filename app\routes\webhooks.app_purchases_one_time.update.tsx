import type { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const { payload, session, topic, shop } = await authenticate.webhook(request);
    
    console.log(`Received ${topic} webhook for ${shop}:`, JSON.stringify(payload, null, 2));
    
    if (payload.app_purchase_one_time) {
      const purchase = payload.app_purchase_one_time;
      
      // TODO: Update purchase in database
      // await db.billingPurchase.upsert({
      //   where: { 
      //     purchaseId: purchase.id 
      //   },
      //   update: {
      //     status: purchase.status,
      //     updatedAt: new Date()
      //   },
      //   create: {
      //     shop,
      //     purchaseId: purchase.id,
      //     status: purchase.status,
      //     amount: parseFloat(purchase.price?.amount || '0'),
      //     currency: purchase.price?.currency_code || 'USD',
      //     productCount: Math.round(parseFloat(purchase.price?.amount || '0') / 0.10),
      //     description: purchase.name
      //   }
      // });
      
      // TODO: Log billing event
      // await db.billingEvent.create({
      //   data: {
      //     shop,
      //     eventType: 'purchase_updated',
      //     referenceId: purchase.id,
      //     eventData: JSON.stringify(purchase)
      //   }
      // });
      
      // Handle specific status changes
      switch (purchase.status) {
        case 'ACCEPTED':
          console.log(`One-time purchase ${purchase.id} was accepted for shop ${shop}`);
          
          // TODO: Trigger optimization process
          // const optimizationRequest = await db.optimizationRequest.findFirst({
          //   where: { purchaseId: purchase.id }
          // });
          // 
          // if (optimizationRequest) {
          //   // Start optimization process
          //   await startOptimizationProcess(optimizationRequest.productIds);
          // }
          
          break;
        case 'DECLINED':
          console.log(`One-time purchase ${purchase.id} was declined for shop ${shop}`);
          break;
        case 'PENDING':
          console.log(`One-time purchase ${purchase.id} is pending for shop ${shop}`);
          break;
      }
    }
    
    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error('App purchase one-time webhook error:', error);
    return new Response("Error processing webhook", { status: 500 });
  }
};

// TODO: Implement optimization process trigger
async function startOptimizationProcess(productIds: string[]) {
  console.log(`Starting optimization for products: ${productIds.join(', ')}`);
  // This would trigger your existing optimization logic
}
