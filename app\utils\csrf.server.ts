/**
 * CSRF Protection Utility
 * Provides Cross-Site Request Forgery protection for billing forms
 */

import { createHash, randomBytes } from "crypto";

export interface CSRFTokenData {
  token: string;
  timestamp: number;
  shop: string;
}

/**
 * Generate a CSRF token for a specific shop
 */
export function generateCSRFToken(shop: string): string {
  const timestamp = Date.now();
  const randomData = randomBytes(32).toString('hex');
  const secret = process.env.SESSION_SECRET || 'default-secret';
  
  // Create token data
  const tokenData: CSRFTokenData = {
    token: randomData,
    timestamp,
    shop
  };
  
  // Create signature
  const payload = JSON.stringify(tokenData);
  const signature = createHash('sha256')
    .update(payload + secret)
    .digest('hex');
  
  // Combine payload and signature
  const csrfToken = Buffer.from(payload).toString('base64') + '.' + signature;
  
  console.log(`🔐 Generated CSRF token for shop: ${shop}`);
  return csrfToken;
}

/**
 * Verify a CSRF token
 */
export function verifyCSRFToken(token: string, shop: string): boolean {
  try {
    if (!token || typeof token !== 'string') {
      console.error('❌ CSRF verification failed: Invalid token format');
      return false;
    }

    const parts = token.split('.');
    if (parts.length !== 2) {
      console.error('❌ CSRF verification failed: Invalid token structure');
      return false;
    }

    const [payloadBase64, signature] = parts;
    
    // Decode payload
    let tokenData: CSRFTokenData;
    try {
      const payload = Buffer.from(payloadBase64, 'base64').toString('utf8');
      tokenData = JSON.parse(payload);
    } catch {
      console.error('❌ CSRF verification failed: Invalid payload');
      return false;
    }

    // Verify shop matches
    if (tokenData.shop !== shop) {
      console.error('❌ CSRF verification failed: Shop mismatch');
      return false;
    }

    // Check token age (valid for 1 hour)
    const now = Date.now();
    const tokenAge = now - tokenData.timestamp;
    const maxAge = 60 * 60 * 1000; // 1 hour

    if (tokenAge > maxAge) {
      console.error('❌ CSRF verification failed: Token expired');
      return false;
    }

    // Verify signature
    const secret = process.env.SESSION_SECRET || 'default-secret';
    const payload = Buffer.from(payloadBase64, 'base64').toString('utf8');
    const expectedSignature = createHash('sha256')
      .update(payload + secret)
      .digest('hex');

    if (signature !== expectedSignature) {
      console.error('❌ CSRF verification failed: Invalid signature');
      return false;
    }

    console.log(`✅ CSRF token verified for shop: ${shop}`);
    return true;

  } catch (error) {
    console.error('❌ CSRF verification error:', error);
    return false;
  }
}

/**
 * Middleware to add CSRF token to loader data
 */
export function addCSRFToken(shop: string, data: any): any {
  return {
    ...data,
    csrfToken: generateCSRFToken(shop)
  };
}

/**
 * Validate CSRF token from form data
 */
export function validateCSRFFromForm(formData: FormData, shop: string): boolean {
  const token = formData.get('csrfToken')?.toString();

  console.log('🔐 CSRF validation - Shop:', shop);
  console.log('🔐 CSRF validation - Token present:', !!token);
  console.log('🔐 CSRF validation - Token length:', token?.length || 0);

  if (!token) {
    console.error('❌ CSRF validation failed: No token provided');
    console.log('📋 Available form data keys:', Array.from(formData.keys()));
    return false;
  }

  const isValid = verifyCSRFToken(token, shop);
  console.log('🔐 CSRF validation result:', isValid);
  return isValid;
}

/**
 * Create CSRF-protected form data
 */
export function createProtectedFormData(shop: string, data: Record<string, string>): FormData {
  const formData = new FormData();
  
  // Add CSRF token
  formData.append('csrfToken', generateCSRFToken(shop));
  
  // Add other data
  for (const [key, value] of Object.entries(data)) {
    formData.append(key, value);
  }
  
  return formData;
}

/**
 * CSRF protection for billing actions
 */
export function validateBillingCSRF(request: Request, shop: string): Promise<boolean> {
  return new Promise(async (resolve) => {
    try {
      // Skip CSRF for GET requests
      if (request.method === 'GET') {
        resolve(true);
        return;
      }

      // Check for CSRF token in form data
      const formData = await request.formData();
      const isValid = validateCSRFFromForm(formData, shop);
      
      resolve(isValid);
    } catch (error) {
      console.error('❌ CSRF validation error:', error);
      resolve(false);
    }
  });
}

/**
 * Generate CSRF meta tags for HTML head
 */
export function generateCSRFMetaTags(shop: string): string {
  const token = generateCSRFToken(shop);
  
  return `
    <meta name="csrf-token" content="${token}">
    <meta name="csrf-shop" content="${shop}">
  `.trim();
}

/**
 * Client-side CSRF token retrieval
 */
export const clientCSRFHelpers = `
  window.getCSRFToken = function() {
    const meta = document.querySelector('meta[name="csrf-token"]');
    return meta ? meta.getAttribute('content') : null;
  };
  
  window.addCSRFToForm = function(formData) {
    const token = window.getCSRFToken();
    if (token) {
      formData.append('csrfToken', token);
    }
    return formData;
  };
  
  window.createProtectedFormData = function(data) {
    const formData = new FormData();
    const token = window.getCSRFToken();
    
    if (token) {
      formData.append('csrfToken', token);
    }
    
    for (const [key, value] of Object.entries(data)) {
      formData.append(key, value);
    }
    
    return formData;
  };
`;

/**
 * Rate limiting for billing actions
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(shop: string, maxRequests: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now();
  const key = `billing:${shop}`;
  
  const current = rateLimitStore.get(key);
  
  if (!current || now > current.resetTime) {
    // Reset or initialize
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + windowMs
    });
    return true;
  }
  
  if (current.count >= maxRequests) {
    console.error(`❌ Rate limit exceeded for shop: ${shop}`);
    return false;
  }
  
  current.count++;
  return true;
}

/**
 * Clean up expired rate limit entries
 */
export function cleanupRateLimit(): void {
  const now = Date.now();
  
  for (const [key, data] of rateLimitStore.entries()) {
    if (now > data.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

// Clean up rate limit entries every 5 minutes
setInterval(cleanupRateLimit, 5 * 60 * 1000);
